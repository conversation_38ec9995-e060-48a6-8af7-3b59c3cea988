#!/usr/bin/env python3
"""
Automatic setup for MiMo-7B Q8_0 model for Knowledge App.
This script will:
1. Install Haystack for RAG functionality
2. Download MiMo-7B-RL-Q8_0.gguf (8.11GB) automatically
3. Set up the model directory structure
4. Verify everything works
"""

import os
import sys
import subprocess
import requests
import logging
from pathlib import Path
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def run_cmd(cmd, timeout=300):
    """Run command with error handling."""
    logger.info(f"🔧 {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.error(f"❌ {result.stderr.strip()}")
            return False
        logger.info("✅ OK")
        return True
    except Exception as e:
        logger.error(f"❌ {e}")
        return False

def install_dependencies():
    """Install required dependencies."""
    logger.info("📦 Installing dependencies...")
    
    commands = [
        "pip install farm-haystack[inference]",
        "pip install sentence-transformers",
        "pip install huggingface-hub",
        "pip install tqdm"
    ]
    
    for cmd in commands:
        if not run_cmd(cmd, timeout=180):
            return False
    
    logger.info("✅ All dependencies installed")
    return True

def create_model_directories():
    """Create necessary model directories."""
    logger.info("📁 Creating model directories...")
    
    directories = [
        "models/gguf_models",
        "models/local",
        "data/models"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ Created: {directory}")
    
    return True

def download_mimo_q8(force_download=False):
    """Download MiMo-7B Q8_0 model automatically."""
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    # Check if already exists
    if os.path.exists(model_path) and not force_download:
        size_mb = os.path.getsize(model_path) / (1024*1024)
        if size_mb > 8000:  # Should be ~8110 MB
            logger.info(f"✅ MiMo Q8_0 already exists ({size_mb:.1f} MB)")
            return True
        else:
            logger.warning(f"⚠️ Existing file too small ({size_mb:.1f} MB), re-downloading...")
    
    logger.info("📥 Downloading MiMo-7B-RL-Q8_0.gguf (8.11GB)...")
    logger.info("⏳ This will take 5-15 minutes depending on your internet speed...")
    
    # HuggingFace download URL
    url = "https://huggingface.co/jedisct1/MiMo-7B-RL-GGUF/resolve/main/MiMo-7B-RL-Q8_0.gguf"
    
    try:
        # Start download with progress bar
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(model_path, 'wb') as file, tqdm(
            desc="Downloading MiMo Q8_0",
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        # Verify download
        final_size = os.path.getsize(model_path)
        final_size_gb = final_size / (1024**3)
        
        if final_size_gb > 7.5:  # Should be ~8.11GB
            logger.info(f"✅ Download complete! Size: {final_size_gb:.2f} GB")
            return True
        else:
            logger.error(f"❌ Download incomplete. Size: {final_size_gb:.2f} GB")
            return False
            
    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        return False

def verify_setup():
    """Verify the complete setup."""
    logger.info("🧪 Verifying setup...")
    
    # Check model file
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    if not os.path.exists(model_path):
        logger.error("❌ Model file not found")
        return False
    
    size_gb = os.path.getsize(model_path) / (1024**3)
    logger.info(f"✅ Model file: {size_gb:.2f} GB")
    
    # Test imports
    try:
        import haystack
        logger.info("✅ Haystack available")
    except ImportError:
        logger.error("❌ Haystack not available")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        logger.info("✅ SentenceTransformers available")
    except ImportError:
        logger.error("❌ SentenceTransformers not available")
        return False
    
    try:
        from llama_cpp import Llama
        logger.info("✅ llama-cpp-python available")
    except ImportError:
        logger.error("❌ llama-cpp-python not available")
        return False
    
    logger.info("✅ All components verified")
    return True

def test_model_loading():
    """Test if the model can be loaded."""
    logger.info("🦙 Testing model loading...")
    
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    try:
        from llama_cpp import Llama
        
        # Test with minimal settings
        logger.info("🔧 Loading model (this may take 30-60 seconds)...")
        model = Llama(
            model_path=model_path,
            n_ctx=2048,  # Small context for testing
            n_gpu_layers=35,  # Use GPU acceleration
            verbose=False
        )
        
        # Test generation
        logger.info("🧪 Testing generation...")
        response = model("What is 2+2?", max_tokens=10, stop=["\n"])
        logger.info(f"✅ Model response: {response['choices'][0]['text'].strip()}")
        
        # Clean up
        del model
        logger.info("✅ Model loading test successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model loading failed: {e}")
        return False

def main():
    """Main setup process."""
    print("🚀 AUTOMATIC MIMO-7B Q8_0 SETUP FOR KNOWLEDGE APP")
    print("=" * 55)
    print("📋 This will:")
    print("  1. Install Haystack & dependencies")
    print("  2. Download MiMo-7B-RL-Q8_0.gguf (8.11GB)")
    print("  3. Set up directory structure")
    print("  4. Verify everything works")
    print("  5. Test model loading")
    print("=" * 55)
    
    # Step 1: Install dependencies
    if not install_dependencies():
        logger.error("❌ Dependency installation failed")
        return False
    
    # Step 2: Create directories
    if not create_model_directories():
        logger.error("❌ Directory creation failed")
        return False
    
    # Step 3: Download model
    if not download_mimo_q8():
        logger.error("❌ Model download failed")
        return False
    
    # Step 4: Verify setup
    if not verify_setup():
        logger.error("❌ Setup verification failed")
        return False
    
    # Step 5: Test model loading
    print("\n🧪 TESTING MODEL LOADING...")
    print("⚠️  This will use your GPU and may take 1-2 minutes")
    test_choice = input("Test model loading now? (y/n): ").lower().strip()
    
    if test_choice == 'y':
        if not test_model_loading():
            logger.error("❌ Model loading test failed")
            return False
    
    print("\n" + "="*55)
    print("🎉 SETUP COMPLETE!")
    print("="*55)
    print("✅ Haystack installed for RAG functionality")
    print("✅ MiMo-7B-RL-Q8_0.gguf downloaded (8.11GB)")
    print("✅ Model directories created")
    print("✅ All dependencies verified")
    print("\n🚀 Your Knowledge App is now ready with:")
    print("   📚 RAG-enhanced question generation")
    print("   🦙 High-quality MiMo-7B Q8_0 model")
    print("   🎯 Maximum quality MCQ generation")
    print("\n💡 Next: Restart the Knowledge App and try generating a quiz!")
    print("="*55)
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ SETUP FAILED - Check errors above")
        sys.exit(1)
