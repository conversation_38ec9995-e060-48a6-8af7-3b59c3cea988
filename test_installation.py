#!/usr/bin/env python3
"""Test the llama-cpp-python installation."""

def test_pytorch():
    """Test PyTorch installation."""
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"✅ GPU: {torch.cuda.get_device_name(0)}")
            print(f"✅ CUDA version: {torch.version.cuda}")
        else:
            print("ℹ️  Running on CPU")
        
        return True
    except Exception as e:
        print(f"❌ PyTorch error: {e}")
        return False

def test_llama_cpp():
    """Test llama-cpp-python installation."""
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python imported successfully")
        
        # Test basic functionality (without loading a model)
        print("✅ llama-cpp-python ready for GGUF models")
        return True
    except Exception as e:
        print(f"❌ llama-cpp-python error: {e}")
        return False

def test_knowledge_app_compatibility():
    """Test compatibility with Knowledge App."""
    try:
        # Test imports that Knowledge App uses
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        
        # Test basic GPU operations if available
        import torch
        if torch.cuda.is_available():
            device = torch.device('cuda')
            x = torch.tensor([1.0, 2.0]).to(device)
            print("✅ GPU tensor operations working")
        
        return True
    except Exception as e:
        print(f"❌ Compatibility error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 TESTING LLAMA-CPP-PYTHON INSTALLATION")
    print("=" * 45)
    
    all_passed = True
    
    print("\n📦 Testing PyTorch...")
    if not test_pytorch():
        all_passed = False
    
    print("\n🦙 Testing llama-cpp-python...")
    if not test_llama_cpp():
        all_passed = False
    
    print("\n🔧 Testing Knowledge App compatibility...")
    if not test_knowledge_app_compatibility():
        all_passed = False
    
    print("\n" + "=" * 45)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Your Knowledge App is ready for GGUF models")
        print("✅ You can now run the Knowledge App")
    else:
        print("❌ Some tests failed")
        print("Check the errors above")
    
    return all_passed

if __name__ == "__main__":
    main()
