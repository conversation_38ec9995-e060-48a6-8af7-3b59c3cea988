#!/usr/bin/env python3
"""
Download a compatible GGUF model that works with current llama-cpp-python.
Since MiMo uses Qwen2 architecture which isn't supported, we'll get a Llama-based model.
"""

import os
import sys
import requests
import logging
from tqdm import tqdm

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def download_compatible_model():
    """Download a Llama-based Q8_0 model that's compatible."""
    
    # Use a well-known Llama 2 7B model that's compatible
    model_url = "https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q8_0.gguf"
    model_path = "models/gguf_models/llama-2-7b-chat.Q8_0.gguf"
    
    logger.info("🦙 Downloading Llama-2-7B-Chat Q8_0 model...")
    logger.info("📁 This model is compatible with your llama-cpp-python version")
    logger.info(f"💾 Size: ~7.16GB")
    logger.info(f"🎯 Quality: Q8_0 (maximum quality)")
    
    # Check if already exists
    if os.path.exists(model_path):
        size_gb = os.path.getsize(model_path) / (1024**3)
        if size_gb > 7.0:  # Should be ~7.16GB
            logger.info(f"✅ Model already exists: {size_gb:.2f} GB")
            return True
        else:
            logger.info(f"⚠️ Existing file too small ({size_gb:.2f} GB), re-downloading...")
    
    try:
        # Create directory
        os.makedirs("models/gguf_models", exist_ok=True)
        
        # Download with progress bar
        response = requests.get(model_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(model_path, 'wb') as file, tqdm(
            desc="Downloading Llama-2-7B Q8_0",
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        # Verify download
        final_size = os.path.getsize(model_path)
        final_size_gb = final_size / (1024**3)
        
        if final_size_gb > 7.0:  # Should be ~7.16GB
            logger.info(f"✅ Download complete! Size: {final_size_gb:.2f} GB")
            return True
        else:
            logger.error(f"❌ Download incomplete. Size: {final_size_gb:.2f} GB")
            return False
            
    except Exception as e:
        logger.error(f"❌ Download failed: {e}")
        return False

def update_model_path():
    """Update the offline generator to use the new model."""
    config_file = "src/knowledge_app/core/offline_mcq_generator.py"
    
    if not os.path.exists(config_file):
        logger.error(f"❌ Config file not found: {config_file}")
        return False
    
    try:
        # Read current file
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update the model path
        old_path = 'self.gguf_model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"'
        new_path = 'self.gguf_model_path = "models/gguf_models/llama-2-7b-chat.Q8_0.gguf"'
        
        if old_path in content:
            content = content.replace(old_path, new_path)
        else:
            # Try the original path too
            old_path2 = 'self.gguf_model_path = "models/gguf_models/mimo-7b-rl-q6_k.gguf"'
            if old_path2 in content:
                content = content.replace(old_path2, new_path)
        
        # Write back the fixed file
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info("✅ Model path updated to Llama-2-7B-Chat")
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to update model path: {e}")
        return False

def test_model_loading():
    """Test if the new model loads correctly."""
    model_path = "models/gguf_models/llama-2-7b-chat.Q8_0.gguf"
    
    logger.info("🧪 Testing Llama-2-7B model loading...")
    
    try:
        from llama_cpp import Llama
        
        logger.info("🔧 Loading model (this may take 1-2 minutes)...")
        
        # Load with minimal settings for testing
        model = Llama(
            model_path=model_path,
            n_ctx=512,  # Small context for testing
            n_gpu_layers=0,  # CPU only for testing
            verbose=False
        )
        
        logger.info("✅ Model loaded successfully!")
        
        # Test generation
        logger.info("🧪 Testing generation...")
        response = model("What is 2+2?", max_tokens=10, stop=["\n"])
        response_text = response['choices'][0]['text'].strip()
        
        logger.info(f"✅ Generation works: {response_text}")
        
        # Clean up
        del model
        logger.info("✅ Model test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model test failed: {e}")
        return False

def main():
    """Main process."""
    print("🦙 COMPATIBLE GGUF MODEL SETUP")
    print("=" * 40)
    print("Since MiMo uses Qwen2 architecture (not supported),")
    print("we'll download Llama-2-7B-Chat Q8_0 instead.")
    print("This model is fully compatible and high quality.")
    print("=" * 40)
    
    # Step 1: Download compatible model
    if not download_compatible_model():
        logger.error("❌ Model download failed")
        return False
    
    # Step 2: Update model path
    if not update_model_path():
        logger.error("❌ Model path update failed")
        return False
    
    # Step 3: Test model loading
    test_choice = input("\nTest model loading now? (y/n): ").lower().strip()
    if test_choice == 'y':
        if not test_model_loading():
            logger.error("❌ Model test failed")
            return False
    
    print("\n" + "="*40)
    print("🎉 COMPATIBLE MODEL SETUP COMPLETE!")
    print("="*40)
    print("✅ Llama-2-7B-Chat Q8_0 downloaded")
    print("✅ Model path updated")
    print("✅ Compatible with your llama-cpp-python")
    print("\n🚀 Your Knowledge App now has:")
    print("   🦙 Local Llama-2-7B Q8_0 inference")
    print("   🎯 Maximum quality MCQ generation")
    print("   ✅ Full compatibility")
    print("   🏠 Offline capability")
    print("\n💡 Restart the Knowledge App to use the new model!")
    print("   python main.py")
    print("="*40)
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ SETUP FAILED")
        sys.exit(1)
