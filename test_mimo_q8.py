#!/usr/bin/env python3
"""
Test MiMo Q8_0 model loading and generation.
"""

import os
import sys
import time
import logging

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def test_model_loading():
    """Test if the MiMo Q8_0 model loads and works."""
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    # Check file exists
    if not os.path.exists(model_path):
        logger.error(f"❌ Model file not found: {model_path}")
        return False
    
    # Check file size
    size_gb = os.path.getsize(model_path) / (1024**3)
    logger.info(f"📁 Model file: {size_gb:.2f} GB")
    
    try:
        from llama_cpp import Llama
        
        logger.info("🔧 Loading MiMo Q8_0 model...")
        logger.info("⚠️  This will use ~10GB VRAM on your RTX 3060")
        
        # Load with optimal settings for RTX 3060 12GB
        start_time = time.time()
        
        model = Llama(
            model_path=model_path,
            n_ctx=4096,  # 4K context
            n_gpu_layers=35,  # Use GPU acceleration
            n_batch=512,  # Batch size
            verbose=False,
            use_mmap=True,  # Memory mapping
            use_mlock=True  # Lock memory
        )
        
        load_time = time.time() - start_time
        logger.info(f"✅ Model loaded in {load_time:.1f} seconds")
        
        # Test generation
        logger.info("🧪 Testing generation...")
        start_time = time.time()
        
        response = model(
            "What is machine learning? Answer briefly:", 
            max_tokens=50, 
            stop=["\n", ".", "?"],
            temperature=0.7
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        response_text = response['choices'][0]['text'].strip()
        tokens_per_sec = 50 / generation_time if generation_time > 0 else 0
        
        logger.info(f"✅ Generation successful!")
        logger.info(f"📝 Response: {response_text}")
        logger.info(f"⚡ Speed: {tokens_per_sec:.1f} tokens/sec")
        logger.info(f"⏱️  Time: {generation_time:.2f} seconds")
        
        # Clean up
        del model
        logger.info("✅ Model test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model test failed: {e}")
        return False

def test_offline_generator():
    """Test the offline MCQ generator with the fixed path."""
    logger.info("🦙 Testing offline MCQ generator...")
    
    try:
        # Add src to path
        sys.path.append('src')
        from knowledge_app.core.offline_mcq_generator import OfflineMCQGenerator
        
        # Initialize generator
        generator = OfflineMCQGenerator()
        
        # Test initialization
        logger.info("🔧 Initializing offline generator...")
        if generator.initialize():
            logger.info("✅ Offline generator initialized!")
            
            # Test generation (async)
            import asyncio
            
            async def test_generation():
                logger.info("🧪 Generating test MCQ...")
                result = await generator.generate_quiz_async(
                    context="machine learning basics",
                    difficulty="medium"
                )
                return result
            
            # Run test
            result = asyncio.run(test_generation())
            
            if result and 'question' in result:
                logger.info("✅ MCQ generation successful!")
                logger.info(f"📝 Question: {result['question'][:100]}...")
                logger.info(f"🔤 Options: {len(result.get('options', []))} choices")
                logger.info(f"✅ Answer: {result.get('correct_answer', 'N/A')}")
                return True
            else:
                logger.error("❌ MCQ generation failed - no result")
                return False
        else:
            logger.error("❌ Offline generator initialization failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Offline generator test failed: {e}")
        return False

def main():
    """Main test process."""
    print("🧪 MIMO Q8_0 MODEL TEST")
    print("=" * 30)
    
    # Test 1: Model loading
    print("\n🔧 Test 1: Model Loading")
    model_works = test_model_loading()
    
    # Test 2: Offline generator
    print("\n🦙 Test 2: Offline MCQ Generator")
    generator_works = test_offline_generator()
    
    # Results
    print("\n" + "="*30)
    print("📊 TEST RESULTS")
    print("="*30)
    print(f"Model Loading: {'✅ PASS' if model_works else '❌ FAIL'}")
    print(f"MCQ Generator: {'✅ PASS' if generator_works else '❌ FAIL'}")
    
    if model_works and generator_works:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ MiMo Q8_0 model is working perfectly")
        print("✅ Offline MCQ generation is ready")
        print("✅ Your Knowledge App now has maximum quality local inference")
        print("\n💡 Restart the Knowledge App to use the new model:")
        print("   python main.py")
    else:
        print("\n❌ Some tests failed")
        print("Check the errors above for details")
    
    return model_works and generator_works

if __name__ == "__main__":
    if not main():
        sys.exit(1)
