#!/usr/bin/env python3
"""
Simple GGUF model test to diagnose issues.
"""

import os
import sys

def test_basic_loading():
    """Test basic GGUF loading with minimal settings."""
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    print(f"🔍 Checking model file: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"❌ File not found: {model_path}")
        return False
    
    size_bytes = os.path.getsize(model_path)
    size_gb = size_bytes / (1024**3)
    print(f"✅ File exists: {size_gb:.2f} GB ({size_bytes:,} bytes)")
    
    try:
        print("🔧 Testing llama-cpp-python import...")
        from llama_cpp import Llama
        print("✅ llama-cpp-python imported successfully")
        
        print("🔧 Attempting minimal model load...")
        print("⚠️  This may take 1-2 minutes...")
        
        # Try with minimal settings first
        model = Llama(
            model_path=model_path,
            n_ctx=512,  # Very small context
            n_gpu_layers=0,  # CPU only first
            verbose=True  # Enable verbose output
        )
        
        print("✅ Model loaded successfully!")
        
        # Test simple generation
        print("🧪 Testing simple generation...")
        response = model("Hello", max_tokens=5)
        print(f"✅ Generation works: {response}")
        
        del model
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"❌ Error type: {type(e).__name__}")
        import traceback
        traceback.print_exc()
        return False

def test_file_integrity():
    """Test if the GGUF file is valid."""
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    print("🔍 Testing file integrity...")
    
    try:
        with open(model_path, 'rb') as f:
            # Read first few bytes to check GGUF header
            header = f.read(8)
            print(f"📄 File header: {header}")
            
            # GGUF files should start with 'GGUF'
            if header.startswith(b'GGUF'):
                print("✅ Valid GGUF header detected")
                return True
            else:
                print("❌ Invalid GGUF header")
                return False
                
    except Exception as e:
        print(f"❌ File read error: {e}")
        return False

def main():
    """Main test."""
    print("🧪 SIMPLE GGUF MODEL TEST")
    print("=" * 30)
    
    # Test file integrity
    print("\n📄 Step 1: File Integrity Check")
    file_ok = test_file_integrity()
    
    if not file_ok:
        print("❌ File integrity check failed - model file may be corrupted")
        return False
    
    # Test basic loading
    print("\n🔧 Step 2: Basic Model Loading")
    load_ok = test_basic_loading()
    
    if load_ok:
        print("\n🎉 SUCCESS! GGUF model is working")
    else:
        print("\n❌ FAILED! Check errors above")
    
    return load_ok

if __name__ == "__main__":
    if not main():
        sys.exit(1)
