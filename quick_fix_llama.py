#!/usr/bin/env python3
"""
QUICK FIX for llama-cpp-python installation issues.
This script takes a direct, no-nonsense approach.
"""

import os
import sys
import subprocess
import shutil
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def run_cmd(cmd, timeout=120):
    """Run command with shorter timeout and better error handling."""
    logger.info(f"🔧 {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.error(f"❌ FAILED: {result.stderr}")
            return False
        if result.stdout.strip():
            logger.info(f"✅ {result.stdout.strip()}")
        return True
    except subprocess.TimeoutExpired:
        logger.error(f"❌ TIMEOUT after {timeout}s")
        return False
    except Exception as e:
        logger.error(f"❌ ERROR: {e}")
        return False

def clean_corrupted_packages():
    """Remove corrupted package directories."""
    logger.info("🧹 Cleaning corrupted packages...")
    
    import site
    site_packages = site.getsitepackages()[0]
    
    for item in os.listdir(site_packages):
        if item.startswith('-orch') or item.startswith('-umpy'):
            corrupted_path = os.path.join(site_packages, item)
            try:
                shutil.rmtree(corrupted_path)
                logger.info(f"✅ Removed: {item}")
            except Exception as e:
                logger.error(f"❌ Failed to remove {item}: {e}")

def install_precompiled_pytorch():
    """Install precompiled PyTorch wheels - much faster."""
    logger.info("🔥 Installing PyTorch (precompiled wheels)...")
    
    # Use pip with specific versions that are known to work
    commands = [
        "pip install --upgrade pip setuptools wheel",
        "pip uninstall torch torchvision torchaudio -y",
        "pip install torch==2.1.0+cu121 torchvision==0.16.0+cu121 torchaudio==2.1.0+cu121 --index-url https://download.pytorch.org/whl/cu121"
    ]
    
    for cmd in commands:
        if not run_cmd(cmd, timeout=300):  # 5 minutes max per command
            return False
    
    return True

def install_llama_cpp_precompiled():
    """Try to install precompiled llama-cpp-python first."""
    logger.info("🦙 Trying precompiled llama-cpp-python...")
    
    # Try precompiled wheel first (much faster)
    if run_cmd("pip install llama-cpp-python --only-binary=all --force-reinstall"):
        logger.info("✅ Precompiled llama-cpp-python installed!")
        return True
    
    logger.info("⚠️ No precompiled wheel available, will compile from source...")
    return False

def compile_llama_cpp_cuda():
    """Compile llama-cpp-python with CUDA support."""
    logger.info("🔨 Compiling llama-cpp-python with CUDA...")
    
    # Set environment for CUDA compilation
    env = os.environ.copy()
    env["CMAKE_ARGS"] = "-DLLAMA_CUBLAS=on"
    env["FORCE_CMAKE"] = "1"
    
    cmd = [
        sys.executable, "-m", "pip", "install", 
        "llama-cpp-python", "--force-reinstall", "--no-cache-dir"
    ]
    
    try:
        logger.info("🔧 Compiling... (this takes 5-10 minutes)")
        result = subprocess.run(cmd, env=env, timeout=600, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ CUDA compilation successful!")
            return True
        else:
            logger.error(f"❌ Compilation failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Compilation timed out")
        return False

def test_installation():
    """Quick test of the installation."""
    logger.info("🧪 Testing installation...")
    
    try:
        import torch
        logger.info(f"✅ PyTorch {torch.__version__}")
        logger.info(f"✅ CUDA available: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            logger.info(f"✅ GPU: {torch.cuda.get_device_name(0)}")
        
        from llama_cpp import Llama
        logger.info("✅ llama-cpp-python imported successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False

def main():
    """Main execution."""
    print("🚀 QUICK LLAMA-CPP-PYTHON FIX")
    print("=" * 40)
    
    # Step 1: Clean up
    clean_corrupted_packages()
    
    # Step 2: Install PyTorch (faster approach)
    if not install_precompiled_pytorch():
        logger.error("❌ PyTorch installation failed")
        return False
    
    # Step 3: Try precompiled llama-cpp-python first
    if not install_llama_cpp_precompiled():
        # Step 4: If no precompiled version, compile with CUDA
        if not compile_llama_cpp_cuda():
            logger.error("❌ llama-cpp-python installation failed")
            return False
    
    # Step 5: Test everything
    if not test_installation():
        logger.error("❌ Installation test failed")
        return False
    
    print("\n🎉 SUCCESS!")
    print("✅ PyTorch with CUDA installed")
    print("✅ llama-cpp-python installed")
    print("✅ Ready for Knowledge App GGUF models")
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ FAILED - Check errors above")
        sys.exit(1)
