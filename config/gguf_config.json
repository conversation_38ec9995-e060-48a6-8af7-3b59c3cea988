{"gguf_engine": {"enabled": true, "priority": "primary", "description": "GGUF engine provides stable, crash-proof inference for consumer hardware"}, "model_paths": {"primary": "models/gguf_models/mimo-7b-rl-q6_k.gguf", "fallback_models": ["models/gguf_models/mimo-7b-rl-q4_k_m.gguf", "models/gguf_models/mimo-7b-rl-q8_0.gguf", "models/gguf_models/mistral-7b-instruct-v0.2.Q5_K_M.gguf"]}, "model_settings": {"n_ctx": 4096, "n_gpu_layers": -1, "n_batch": 512, "use_mmap": true, "use_mlock": false, "low_vram": true}, "generation_params": {"max_tokens": 700, "temperature": 0.7, "top_p": 0.9, "top_k": 40, "repeat_penalty": 1.1, "stop_sequences": ["\n\n", "###", "---", "Question:"]}, "recommended_models": {"mimo_7b_rl": {"name": "MiMo-7B-RL (SUPERIOR TO OPENAI o1-mini!)", "source": "jedisct1/MiMo-7B-RL-GGUF", "file": "mimo-7b-rl-q6_k.gguf", "size_gb": 6.26, "description": "🔥 BEATS OpenAI o1-mini! 95.8% MATH-500, 68.2% AIME 2024, 57.8% LiveCodeBench v5", "performance": {"math_500": "95.8%", "aime_2024": "68.2%", "aime_2025": "55.4%", "livecode_v5": "57.8%", "livecode_v6": "49.3%"}}, "mimo_7b_rl_q4": {"name": "MiMo-7B-RL (Q4 - Smaller)", "source": "jedisct1/MiMo-7B-RL-GGUF", "file": "mimo-7b-rl-q4_k_m.gguf", "size_gb": 4.68, "description": "Smaller version for lower VRAM systems"}, "mimo_7b_rl_q8": {"name": "MiMo-7B-RL (Q8 - Highest Quality)", "source": "jedisct1/MiMo-7B-RL-GGUF", "file": "mimo-7b-rl-q8_0.gguf", "size_gb": 8.11, "description": "Highest quality version for systems with more VRAM"}}, "installation_guide": {"step1": "Install llama-cpp-python with CUDA support", "command": "pip install llama-cpp-python --force-reinstall --upgrade --no-cache-dir", "step2": "Download GGUF model from Hugging Face", "step3": "Place model in models/gguf_models/ directory", "step4": "Restart application to detect new model"}, "troubleshooting": {"compilation_failed": {"issue": "llama-cpp-python compilation fails on Windows", "solution": "Install Visual Studio Build Tools or use pre-compiled wheels"}, "cuda_not_detected": {"issue": "CUDA not available for GGUF engine", "solution": "Model will run on CPU (slower but functional)"}, "model_not_found": {"issue": "No GGUF models detected", "solution": "Download recommended models and place in models/gguf_models/"}, "insufficient_memory": {"issue": "Model too large for available VRAM", "solution": "Use Q4_K_M quantization or enable CPU offloading"}}}