#!/usr/bin/env python3
"""
Quick setup for Knowledge App dependencies.
This installs Haystack and other dependencies to fix MCQ generation immediately.
"""

import os
import sys
import subprocess
import logging

logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)

def run_cmd(cmd, timeout=180):
    """Run command with error handling."""
    logger.info(f"🔧 {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode != 0:
            logger.error(f"❌ {result.stderr.strip()}")
            return False
        logger.info("✅ OK")
        return True
    except Exception as e:
        logger.error(f"❌ {e}")
        return False

def install_haystack():
    """Install Haystack for RAG functionality."""
    logger.info("📚 Installing Haystack and dependencies...")
    
    commands = [
        "pip install farm-haystack[inference]",
        "pip install sentence-transformers",
        "pip install transformers"
    ]
    
    for cmd in commands:
        if not run_cmd(cmd):
            return False
    
    logger.info("✅ Haystack and dependencies installed")
    return True

def test_imports():
    """Test that all imports work."""
    logger.info("🧪 Testing imports...")
    
    try:
        import haystack
        logger.info("✅ Haystack available")
    except ImportError:
        logger.error("❌ Haystack not available")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        logger.info("✅ SentenceTransformers available")
    except ImportError:
        logger.error("❌ SentenceTransformers not available")
        return False
    
    try:
        from llama_cpp import Llama
        logger.info("✅ llama-cpp-python available")
    except ImportError:
        logger.error("❌ llama-cpp-python not available")
        return False
    
    logger.info("✅ All imports successful")
    return True

def check_model_status():
    """Check the status of the MiMo model download."""
    model_path = "models/gguf_models/MiMo-7B-RL-Q8_0.gguf"
    
    if os.path.exists(model_path):
        size_gb = os.path.getsize(model_path) / (1024**3)
        if size_gb > 7.5:  # Should be ~8.11GB
            logger.info(f"✅ MiMo Q8_0 model ready: {size_gb:.2f} GB")
            return True
        else:
            logger.info(f"⏳ MiMo Q8_0 downloading: {size_gb:.2f} GB / 8.11 GB")
            return False
    else:
        logger.info("❌ MiMo Q8_0 model not found")
        return False

def main():
    """Quick setup process."""
    print("🚀 QUICK KNOWLEDGE APP DEPENDENCY SETUP")
    print("=" * 45)
    print("This will install Haystack to fix MCQ generation immediately.")
    print("The MiMo model download can continue in the background.")
    print("=" * 45)
    
    # Install dependencies
    if not install_haystack():
        logger.error("❌ Dependency installation failed")
        return False
    
    # Test imports
    if not test_imports():
        logger.error("❌ Import test failed")
        return False
    
    # Check model status
    model_ready = check_model_status()
    
    print("\n" + "="*45)
    print("🎉 DEPENDENCY SETUP COMPLETE!")
    print("="*45)
    print("✅ Haystack installed for RAG functionality")
    print("✅ SentenceTransformers available")
    print("✅ llama-cpp-python working")
    
    if model_ready:
        print("✅ MiMo Q8_0 model ready")
        print("\n🚀 Your Knowledge App is fully ready!")
    else:
        print("⏳ MiMo Q8_0 model still downloading")
        print("\n💡 You can now:")
        print("   1. Restart the Knowledge App")
        print("   2. Try generating quizzes (will use RAG)")
        print("   3. When model download completes, you'll get offline generation too")
    
    print("\n🔧 To restart the Knowledge App:")
    print("   python main.py")
    print("="*45)
    
    return True

if __name__ == "__main__":
    if not main():
        print("\n❌ SETUP FAILED")
        sys.exit(1)
